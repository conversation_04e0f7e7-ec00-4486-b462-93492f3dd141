"""
OpenTelemetry Configuration
Provides flag-based initialization and instrumentation for observability.
"""

import os
import logging
from typing import Optional

# OpenTelemetry Core imports
from opentelemetry import trace, metrics
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from opentelemetry.sdk.metrics import MeterProvider
from opentelemetry.sdk.metrics.export import PeriodicExportingMetricReader
from opentelemetry.sdk.resources import Resource
from opentelemetry.semconv.resource import ResourceAttributes

# OTLP Exporters
from opentelemetry.exporter.otlp.proto.grpc.trace_exporter import OTLPSpanExporter
from opentelemetry.exporter.otlp.proto.grpc.metric_exporter import OTLPMetricExporter
from opentelemetry.exporter.otlp.proto.grpc._log_exporter import OTLPLogExporter

# Logging SDK imports
from opentelemetry._logs import set_logger_provider, get_logger_provider
from opentelemetry.sdk._logs import <PERSON><PERSON><PERSON><PERSON><PERSON>, Logging<PERSON>andler
from opentelemetry.sdk._logs.export import BatchLogRecordProcessor

# Auto-instrumentation imports
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
from opentelemetry.instrumentation.httpx import HTTPXClientInstrumentor
from opentelemetry.instrumentation.logging import LoggingInstrumentor

# FastAPI middleware imports
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

logger = logging.getLogger(__name__)

# Global variables to track initialization
_otel_initialized = False
_tracer: Optional[trace.Tracer] = None
_meter: Optional[metrics.Meter] = None

def is_otel_enabled() -> bool:
    """Check if OpenTelemetry is enabled via standard OTEL environment variable."""
    return os.getenv("OTEL_SDK_DISABLED", "false").lower() == "false"

def get_otel_config() -> dict:
    """Get OpenTelemetry configuration from standard OTEL environment variables."""
    # Parse OTEL headers
    headers_str = os.getenv("OTEL_EXPORTER_OTLP_HEADERS", "")
    headers = _parse_headers(headers_str)
    
    return {
        "endpoint": os.getenv("OTEL_EXPORTER_OTLP_ENDPOINT", "http://localhost:4317"),
        "service_name": os.getenv("OTEL_SERVICE_NAME", "rozana-oms-service"),
        "service_version": os.getenv("OTEL_SERVICE_VERSION", "4.0.0"),
        "environment": os.getenv("ENVIRONMENT", "development"),
        "insecure": os.getenv("OTEL_EXPORTER_OTLP_INSECURE", "false").lower() == "true",
        "headers": headers,
        "protocol": os.getenv("OTEL_EXPORTER_OTLP_PROTOCOL", "grpc"),
        "excluded_urls": os.getenv("OTEL_INSTRUMENTATION_HTTP_EXCLUDED_URLS", "/health,/healthz,/ready,/alive,/ping,/status").split(","),
    }

def _parse_headers(headers_str: str) -> dict:
    """Parse headers from environment variable string format."""
    headers = {}
    if headers_str:
        for header in headers_str.split(","):
            if "=" in header:
                key, value = header.split("=", 1)
                headers[key.strip()] = value.strip()
    return headers

def init_otel() -> bool:
    """
    Initialize OpenTelemetry instrumentation using standard OTEL environment variables.
    Returns True if successfully initialized, False otherwise.
    """
    global _otel_initialized, _tracer, _meter
    
    if not is_otel_enabled():
        logger.info("OpenTelemetry is disabled (OTEL_SDK_DISABLED=true)")
        return False
    
    if _otel_initialized:
        logger.info("OpenTelemetry already initialized")
        return True
    
    try:
        config = get_otel_config()
        logger.info(f"Initializing OpenTelemetry with endpoint: {config['endpoint']}")
        
        # Create resource
        resource = Resource.create({
            ResourceAttributes.SERVICE_NAME: config["service_name"],
            ResourceAttributes.SERVICE_VERSION: config["service_version"],
            ResourceAttributes.DEPLOYMENT_ENVIRONMENT: config["environment"],
        })
        
        # Initialize tracing
        trace_exporter = OTLPSpanExporter(
            endpoint=config["endpoint"],
            insecure=config["insecure"],
            headers=config["headers"]
        )
        
        tracer_provider = TracerProvider(resource=resource)
        span_processor = BatchSpanProcessor(trace_exporter)
        tracer_provider.add_span_processor(span_processor)
        trace.set_tracer_provider(tracer_provider)
        
        # Initialize metrics
        metric_exporter = OTLPMetricExporter(
            endpoint=config["endpoint"],
            insecure=config["insecure"],
            headers=config["headers"]
        )
        
        metric_reader = PeriodicExportingMetricReader(
            exporter=metric_exporter,
            export_interval_millis=30000  # Export every 30 seconds
        )
        
        meter_provider = MeterProvider(
            resource=resource,
            metric_readers=[metric_reader]
        )
        metrics.set_meter_provider(meter_provider)
        
        # Initialize logging export
        log_exporter = OTLPLogExporter(
            endpoint=config["endpoint"],
            insecure=config["insecure"],
            headers=config["headers"]
        )
        
        logger_provider = LoggerProvider(resource=resource)
        log_processor = BatchLogRecordProcessor(log_exporter)
        logger_provider.add_log_record_processor(log_processor)
        set_logger_provider(logger_provider)
        
        # Get tracer and meter instances
        _tracer = trace.get_tracer(__name__)
        _meter = metrics.get_meter(__name__)
        
        # Initialize automatic instrumentation
        _setup_auto_instrumentation()
        
        _otel_initialized = True
        logger.info("OpenTelemetry initialized successfully")
        
        # Configure logging with trace correlation
        configure_logging_with_traces()
        
        return True
        
    except Exception as e:
        logger.error(f"Failed to initialize OpenTelemetry: {e}")
        return False

def _setup_auto_instrumentation():
    """Setup automatic instrumentation for various libraries."""
    try:
        # FastAPI instrumentation will be done in main.py after app creation
        
        # HTTP client instrumentation
        HTTPXClientInstrumentor().instrument()
        logger.debug("HTTPX instrumentation enabled")
        
        # Logging instrumentation (without set_logging_format to avoid conflicts)
        LoggingInstrumentor().instrument()
        
        # Add OTEL logging handler to send logs to OpenTelemetry
        if _otel_initialized:
            logger_provider = get_logger_provider()
            if logger_provider:
                handler = LoggingHandler(level=logging.NOTSET, logger_provider=logger_provider)
                # Add handler to root logger to capture all logs
                root_logger = logging.getLogger()
                root_logger.addHandler(handler)
                logger.debug("OTEL logging handler added for log export to OpenTelemetry")
        
        logger.debug("Logging instrumentation enabled")
        
    except Exception as e:
        logger.warning(f"Some auto-instrumentation failed: {e}")

def instrument_fastapi_app(app):
    """Instrument FastAPI app with OpenTelemetry."""
    if not is_otel_enabled() or not _otel_initialized:
        return
    
    try:
        # Simple FastAPI instrumentation without problematic parameters
        FastAPIInstrumentor.instrument_app(app, excluded_urls="/health")
        logger.info("FastAPI instrumentation enabled")
    except Exception as e:
        logger.error(f"Failed to instrument FastAPI app: {e}")

def get_tracer() -> Optional[trace.Tracer]:
    """Get the OpenTelemetry tracer instance."""
    return _tracer if _otel_initialized else None

def get_meter() -> Optional[metrics.Meter]:
    """Get the OpenTelemetry meter instance."""
    return _meter if _otel_initialized else None

def create_span(name: str, **kwargs):
    """Create a new span if OpenTelemetry is enabled."""
    if not _otel_initialized or not _tracer:
        return trace.NoOpSpan()
    
    return _tracer.start_span(name, **kwargs)

def add_span_attribute(span, key: str, value):
    """Add attribute to span if OpenTelemetry is enabled."""
    if _otel_initialized and span and hasattr(span, 'set_attribute'):
        try:
            span.set_attribute(key, value)
        except Exception as e:
            logger.debug(f"Failed to add span attribute: {e}")

def record_metric(name: str, value: float, attributes: dict = None):
    """Record a metric if OpenTelemetry is enabled."""
    if not _otel_initialized or not _meter:
        return
    
    try:
        counter = _meter.create_counter(name)
        counter.add(value, attributes or {})
    except Exception as e:
        logger.debug(f"Failed to record metric: {e}")

class TraceFormatter(logging.Formatter):
    """Custom formatter that adds trace and span IDs to log records."""
    
    def format(self, record):
        # Get current span context
        span = trace.get_current_span()
        if span and span.is_recording():
            span_context = span.get_span_context()
            record.trace_id = f"{span_context.trace_id:032x}"
            record.span_id = f"{span_context.span_id:016x}"
        else:
            record.trace_id = "0" * 32
            record.span_id = "0" * 16
        
        return super().format(record)

def configure_logging_with_traces():
    """Configure logging to include trace correlation and export to SigNoz."""
    if not is_otel_enabled() or not _otel_initialized:
        return
    
    try:
        # Configure root logger with trace correlation
        root_logger = logging.getLogger()
        if root_logger.handlers:
            for handler in root_logger.handlers:
                if isinstance(handler, logging.StreamHandler):
                    # Add trace and span IDs to log format
                    formatter = TraceFormatter(
                        '%(asctime)s - %(name)s - %(levelname)s'
                    )
                    handler.setFormatter(formatter)
        
        # Add OTEL logging handler to export logs to OpenTelemetry
        logger_provider = get_logger_provider()
        if logger_provider:
            otel_handler = LoggingHandler(logger_provider=logger_provider)
            otel_handler.setLevel(logging.INFO)  # Only send INFO and above to OpenTelemetry
            
            # Add OTEL handler to root logger to capture all application logs
            root_logger.addHandler(otel_handler)
            
            # Also add to specific app loggers to ensure capture
            app_logger = logging.getLogger('app')
            if not any(isinstance(h, LoggingHandler) for h in app_logger.handlers):
                app_logger.addHandler(otel_handler)
        
        logger.info("Logging configured with trace correlation and OpenTelemetry export")
        
    except Exception as e:
        logger.warning(f"Failed to configure logging with traces: {e}")

def shutdown_otel():
    """Shutdown OpenTelemetry instrumentation."""
    global _otel_initialized
    
    if not _otel_initialized:
        return
    
    try:
        # Shutdown tracer provider
        tracer_provider = trace.get_tracer_provider()
        if hasattr(tracer_provider, 'shutdown'):
            tracer_provider.shutdown()
        
        # Shutdown meter provider
        meter_provider = metrics.get_meter_provider()
        if hasattr(meter_provider, 'shutdown'):
            meter_provider.shutdown()
        
        _otel_initialized = False
        logger.info("OpenTelemetry shutdown completed")
        
    except Exception as e:
        logger.error(f"Error during OpenTelemetry shutdown: {e}")
