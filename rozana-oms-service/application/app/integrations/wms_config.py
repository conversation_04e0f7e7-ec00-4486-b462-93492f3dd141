import os
from typing import Dict, Any, Optional
from dataclasses import dataclass
from dotenv import load_dotenv
import logging

load_dotenv()
logger = logging.getLogger(__name__)

@dataclass
class WMSConfig:
    """WMS Configuration settings"""

    # Authentication
    client_id: str
    client_secret: str

    # API Settings
    base_url: str
    
    # Integration Control
    integration_enabled: bool = True
    timeout: int = 60

    # Token Management
    token_buffer_minutes: int = 5
    token_retry_attempts: int = 3

    # Order Settings
    default_order_type: str = "Express"
    default_delivery_charge: float = 40.0
    default_handling_charge: float = 4.0

    # Retry Settings
    max_retries: int = 3
    retry_delay_seconds: int = 2

    @classmethod
    def from_environment(cls) -> 'WMSConfig':
        """Create configuration from environment variables"""

        # Integration control
        integration_enabled = os.getenv("WMS_INTEGRATION_ENABLED", "false").lower() == "true"

        # If integration is disabled, return minimal config
        if not integration_enabled:
            return cls(
                integration_enabled=False,
                client_id="",
                client_secret="",
                base_url=""
            )

        # Required settings when integration is enabled
        client_id = os.getenv("WMS_CLIENT_ID")
        client_secret = os.getenv("WMS_CLIENT_SECRET")
        base_url = os.getenv("WMS_BASE_URL")

        if not client_id or not client_secret or not base_url:
            raise ValueError("WMS_CLIENT_ID, WMS_CLIENT_SECRET, and WMS_BASE_URL are required when WMS_INTEGRATION_ENABLED=true")

        return cls(
            # Integration Control
            integration_enabled=integration_enabled,
            # Authentication
            client_id=client_id,
            client_secret=client_secret,

            # API Settings
            base_url=base_url,
            timeout=int(os.getenv("WMS_TIMEOUT", "60")),

            # Token Management
            token_buffer_minutes=int(os.getenv("WMS_TOKEN_BUFFER_MINUTES", "5")),
            token_retry_attempts=int(os.getenv("WMS_TOKEN_RETRY_ATTEMPTS", "3")),

            # Order Settings
            default_order_type=os.getenv("WMS_DEFAULT_ORDER_TYPE", "Express"),
            default_delivery_charge=float(os.getenv("WMS_DEFAULT_DELIVERY_CHARGE", "40.0")),
            default_handling_charge=float(os.getenv("WMS_DEFAULT_HANDLING_CHARGE", "4.0")),

            # Retry Settings
            max_retries=int(os.getenv("WMS_MAX_RETRIES", "3")),
            retry_delay_seconds=int(os.getenv("WMS_RETRY_DELAY_SECONDS", "2")),
        )

    def validate(self) -> bool:
        """Validate configuration settings"""
        try:
            # Check required fields
            if not self.client_id or not self.client_secret:
                logger.error("Missing required authentication credentials")
                return False
            
            if not self.base_url:
                logger.error("Missing required API settings")
                return False
            
            # Check numeric ranges
            if self.timeout <= 0 or self.timeout > 300:
                logger.error(f"Invalid timeout value: {self.timeout}")
                return False
            
            if self.max_retries < 0 or self.max_retries > 10:
                logger.error(f"Invalid max_retries value: {self.max_retries}")
                return False
            
            logger.info("WMS configuration validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Configuration validation failed: {str(e)}")
            return False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary (excluding sensitive data)"""
        return {
            "base_url": self.base_url,
            "timeout": self.timeout,
            "default_order_type": self.default_order_type,
            "default_delivery_charge": self.default_delivery_charge,
            "default_handling_charge": self.default_handling_charge,
            "max_retries": self.max_retries,
            "retry_delay_seconds": self.retry_delay_seconds,
            "has_credentials": bool(self.client_id and self.client_secret)
        }

class WMSConfigManager:
    """Manages WMS configuration with validation"""
    
    def __init__(self):
        self._config: Optional[WMSConfig] = None
    
    def get_config(self) -> WMSConfig:
        """Get current configuration, loading if necessary"""
        if self._config is None:
            self._load_config()
        return self._config
    
    def _load_config(self) -> bool:
        """Load configuration from environment"""
        try:
            new_config = WMSConfig.from_environment()
            self._config = new_config
            
            if new_config.validate():
                logger.info("WMS configuration loaded successfully")
                return True
            else:
                logger.error("WMS configuration validation failed")
                return False
                
        except Exception as e:
            logger.error(f"Failed to load WMS configuration: {str(e)}")
            return False
    
    def get_config_summary(self) -> Dict[str, Any]:
        """Get configuration summary for monitoring"""
        if self._config is None:
            return {"status": "not_loaded"}
        
        return {
            "status": "loaded",
            "config": self._config.to_dict()
        }

# Global configuration manager
wms_config_manager = WMSConfigManager()

# Convenience function to get current config
def get_wms_config() -> WMSConfig:
    """Get current WMS configuration"""
    return wms_config_manager.get_config()
