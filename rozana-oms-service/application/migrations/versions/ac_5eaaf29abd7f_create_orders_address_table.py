"""create_orders_address_table

Revision ID: 5eaaf29abd7f
Revises: dd55a25d4835
Create Date: 2025-07-26 07:16:55.989719

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '5eaaf29abd7f'
down_revision: Union[str, Sequence[str], None] = 'dd55a25d4835'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('order_addresses',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('order_id', sa.Integer(), nullable=False),
        sa.Column('full_name', sa.String(length=200), nullable=False),
        sa.Column('phone_number', sa.String(length=20), nullable=False),
        sa.Column('address_line1', sa.String(length=255), nullable=False),
        sa.Column('address_line2', sa.String(length=255), nullable=True),
        sa.Column('city', sa.String(length=100), nullable=False),
        sa.Column('state', sa.String(length=100), nullable=False),
        sa.Column('postal_code', sa.String(length=20), nullable=False),
        sa.Column('country', sa.String(length=100), nullable=False),
        sa.Column('type_of_address', sa.String(length=20), nullable=False),
        sa.Column('created_at', sa.TIMESTAMP(timezone=True), nullable=False, server_default=sa.text("timezone('Asia/Kolkata', current_timestamp)")),
        sa.Column('updated_at', sa.TIMESTAMP(timezone=True), nullable=False, server_default=sa.text("timezone('Asia/Kolkata', current_timestamp)")),
        sa.ForeignKeyConstraint(['order_id'], ['orders.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_order_addresses_city_state', 'order_addresses', ['city', 'state'], unique=False)
    op.create_index('idx_order_addresses_order_type', 'order_addresses', ['order_id', 'type_of_address'], unique=False)
    op.create_index('idx_order_addresses_postal_country', 'order_addresses', ['postal_code', 'country'], unique=False)
    op.create_index(op.f('ix_order_addresses_id'), 'order_addresses', ['id'], unique=False)
    op.create_index(op.f('ix_order_addresses_order_id'), 'order_addresses', ['order_id'], unique=False)

    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_order_addresses_order_id'), table_name='order_addresses')
    op.drop_index(op.f('ix_order_addresses_id'), table_name='order_addresses')
    op.drop_index('idx_order_addresses_postal_country', table_name='order_addresses')
    op.drop_index('idx_order_addresses_order_type', table_name='order_addresses')
    op.drop_index('idx_order_addresses_city_state', table_name='order_addresses')
    op.drop_table('order_addresses')
    # ### end Alembic commands ###
