services:

  db1:
    image: postgres:15
    environment:
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
      POSTGRES_DB: oms_db
    volumes:
      - db1-data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U user -d oms_db"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  app:
    container_name: oms-api
    build: .
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
    stdin_open: true
    tty: true
    ports:
      - "8000:8000"
    env_file:
      - .env
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8000/health || exit 1"]
      interval: 10s
      timeout: 5s
      retries: 5
    depends_on:
      db1:
        condition: service_healthy
    volumes:
      - ./application:/application

volumes:
  db1-data: