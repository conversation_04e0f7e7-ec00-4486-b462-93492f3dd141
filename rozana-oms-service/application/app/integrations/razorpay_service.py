"""
Razorpay service for handling payment operations in the OMS system.

This module provides the main interface for Razorpay payment gateway integration,
including order creation, payment verification, and webhook handling.
"""

import razorpay
import hashlib
import hmac
import json
import logging
from typing import Dict, Any, Optional, Tuple
from datetime import datetime

from app.integrations.razorpay_config import razorpay_config
from app.core.constants import OrderStatus


logger = logging.getLogger(__name__)


class RazorpayService:
    """Service class for Razorpay payment operations"""
    
    def __init__(self):
        self.config = razorpay_config
        self.client = None
        
        if self.config.integration_enabled:
            try:
                self.client = razorpay.Client(
                    auth=(self.config.key_id, self.config.key_secret)
                )
                logger.info("Razorpay client initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize Razorpay client: {e}")
                raise
        else:
            logger.info("Razorpay integration is disabled")
    
    async def create_razorpay_order(
        self, 
        order_id: str, 
        amount: float, 
        customer_details: Dict[str, Any],
        notes: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create a Razorpay order for payment processing
        
        Args:
            order_id: OMS order ID
            amount: Order amount in rupees
            customer_details: Customer information
            notes: Additional notes for the order
            
        Returns:
            Dict containing Razorpay order details or error info
        """
        if not self.config.integration_enabled:
            logger.info(f"Razorpay integration disabled - skipping order creation for {order_id}")
            return {
                "success": False,
                "skipped": True,
                "message": "Razorpay integration is disabled"
            }
        
        try:
            # Convert amount to paise (Razorpay expects amount in smallest currency unit)
            amount_paise = int(amount * 100)
            
            # Prepare order data
            order_data = {
                "amount": amount_paise,
                "currency": self.config.currency,
                "receipt": order_id,
                "notes": notes or {}
            }
            
            # Add customer details to notes
            order_data["notes"].update({
                "oms_order_id": order_id,
                "customer_id": customer_details.get("customer_id", ""),
                "customer_name": customer_details.get("customer_name", ""),
                "created_at": datetime.utcnow().isoformat()
            })
            
            # Create order in Razorpay
            razorpay_order = self.client.order.create(data=order_data)
            
            logger.info(f"Razorpay order created successfully: {razorpay_order['id']} for OMS order: {order_id}")
            
            return {
                "success": True,
                "razorpay_order_id": razorpay_order["id"],
                "amount": amount,
                "amount_paise": amount_paise,
                "currency": razorpay_order["currency"],
                "status": razorpay_order["status"],
                "key_id": self.config.key_id,
                "created_at": razorpay_order["created_at"]
            }
            
        except Exception as e:
            logger.error(f"Failed to create Razorpay order for {order_id}: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to create Razorpay order"
            }
    
    async def verify_payment_signature(
        self, 
        razorpay_order_id: str,
        razorpay_payment_id: str,
        razorpay_signature: str
    ) -> bool:
        """
        Verify Razorpay payment signature for security
        
        Args:
            razorpay_order_id: Razorpay order ID
            razorpay_payment_id: Razorpay payment ID
            razorpay_signature: Signature to verify
            
        Returns:
            True if signature is valid, False otherwise
        """
        if not self.config.integration_enabled:
            logger.warning("Razorpay integration disabled - cannot verify signature")
            return False
        
        try:
            # Create signature string
            signature_string = f"{razorpay_order_id}|{razorpay_payment_id}"
            
            # Generate expected signature
            expected_signature = hmac.new(
                self.config.key_secret.encode(),
                signature_string.encode(),
                hashlib.sha256
            ).hexdigest()
            
            # Compare signatures
            is_valid = hmac.compare_digest(expected_signature, razorpay_signature)
            
            if is_valid:
                logger.info(f"Payment signature verified for order: {razorpay_order_id}")
            else:
                logger.warning(f"Invalid payment signature for order: {razorpay_order_id}")
            
            return is_valid
            
        except Exception as e:
            logger.error(f"Error verifying payment signature: {e}")
            return False
    
    async def verify_webhook_signature(self, payload: str, signature: str) -> bool:
        """
        Verify webhook signature from Razorpay
        
        Args:
            payload: Webhook payload
            signature: Webhook signature
            
        Returns:
            True if signature is valid, False otherwise
        """
        if not self.config.integration_enabled:
            logger.warning("Razorpay integration disabled - cannot verify webhook")
            return False
        
        try:
            expected_signature = hmac.new(
                self.config.webhook_secret.encode(),
                payload.encode(),
                hashlib.sha256
            ).hexdigest()
            
            return hmac.compare_digest(expected_signature, signature)
            
        except Exception as e:
            logger.error(f"Error verifying webhook signature: {e}")
            return False
    
    async def get_payment_details(self, payment_id: str) -> Dict[str, Any]:
        """
        Fetch payment details from Razorpay
        
        Args:
            payment_id: Razorpay payment ID
            
        Returns:
            Payment details or error info
        """
        if not self.config.integration_enabled:
            return {
                "success": False,
                "skipped": True,
                "message": "Razorpay integration is disabled"
            }
        
        try:
            payment = self.client.payment.fetch(payment_id)
            
            return {
                "success": True,
                "payment": payment
            }
            
        except Exception as e:
            logger.error(f"Failed to fetch payment details for {payment_id}: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to fetch payment details"
            }
    



# Global service instance
razorpay_service = RazorpayService()
