"""
Payment routes for Razorpay integration in the OMS system.

This module handles payment-related API endpoints including order creation,
payment verification, and webhook handling.
"""

import json
import logging
from fastapi import APIRouter, Request, HTTPException, BackgroundTasks

from app.dto.payments import (
    PaymentOrderCreate,
    PaymentOrderResponse,
    PaymentVerification,
    PaymentVerificationResponse,
)
from app.integrations.razorpay_service import razorpay_service
from app.core.order_functions import get_order_by_id, create_payment_for_order, update_payment_status, get_payment_status_for_order
from app.core.constants import PaymentStatus

logger = logging.getLogger(__name__)

payment_router = APIRouter(tags=["payments"])


@payment_router.post("/create_payment_order", response_model=PaymentOrderResponse)
async def create_payment_order(payment_order: PaymentOrderCreate, request: Request):
    """
    Create a Razorpay order for payment processing.
    
    This endpoint creates a corresponding Razorpay order for an existing OMS order,
    enabling the Flutter app to initiate payment.
    """
    try:
        # Verify that the OMS order exists
        order = await get_order_by_id(payment_order.order_id)
        if not order:
            raise HTTPException(
                status_code=404,
                detail=f"Order {payment_order.order_id} not found"
            )
        
        # Prepare customer details
        customer_details = {
            "customer_id": payment_order.customer_id,
            "customer_name": payment_order.customer_name,
            "customer_email": payment_order.customer_email,
            "customer_phone": payment_order.customer_phone
        }
        
        # Create Razorpay order
        result = await razorpay_service.create_razorpay_order(
            order_id=payment_order.order_id,
            amount=payment_order.amount,
            customer_details=customer_details,
            notes=payment_order.notes
        )
        
        if result["success"]:
            return PaymentOrderResponse(
                success=True,
                message="Payment order created successfully",
                razorpay_order_id=result["razorpay_order_id"],
                amount=result["amount"],
                amount_paise=result["amount_paise"],
                currency=result["currency"],
                key_id=result["key_id"],
                order_id=payment_order.order_id,
                created_at=result["created_at"]
            )
        else:
            if result.get("skipped"):
                return PaymentOrderResponse(
                    success=False,
                    message="Razorpay integration is disabled"
                )
            else:
                raise HTTPException(
                    status_code=500,
                    detail=result.get("message", "Failed to create payment order")
                )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating payment order: {e}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error while creating payment order"
        )


@payment_router.post("/verify_payment", response_model=PaymentVerificationResponse)
async def verify_payment(verification: PaymentVerification, background_tasks: BackgroundTasks):
    """
    Verify payment signature and update order status.
    
    This endpoint is called by the Flutter app after successful payment
    to verify the payment and update the order status.
    """
    try:
        # Verify payment signature
        is_verified = await razorpay_service.verify_payment_signature(
            verification.razorpay_order_id,
            verification.razorpay_payment_id,
            verification.razorpay_signature
        )
        
        if not is_verified:
            return PaymentVerificationResponse(
                success=False,
                message="Payment signature verification failed",
                verified=False
            )
        
        # Get payment details from Razorpay
        payment_result = await razorpay_service.get_payment_details(
            verification.razorpay_payment_id
        )
        
        if not payment_result["success"]:
            return PaymentVerificationResponse(
                success=False,
                message="Failed to fetch payment details",
                verified=True
            )
        
        payment = payment_result["payment"]
        razorpay_status = payment["status"]
        
        # Map Razorpay status to our payment status
        payment_status = PaymentStatus.COMPLETED if razorpay_status == "captured" else PaymentStatus.FAILED
        
        # Create payment record if it doesn't exist
        payment_amount = float(payment["amount"]) / 100  # Razorpay amount is in paise
        background_tasks.add_task(
            create_payment_for_order,
            verification.oms_order_id,
            verification.razorpay_payment_id,
            payment_amount,
            "online"
        )
        
        # Update payment status in background (payments table only)
        background_tasks.add_task(
            update_payment_status,
            verification.razorpay_payment_id,
            payment_status
        )
        
        return PaymentVerificationResponse(
            success=True,
            message="Payment verified successfully",
            verified=True,
            order_id=verification.oms_order_id,
            payment_status=str(payment_status)  # Convert integer to string
        )
    
    except Exception as e:
        logger.error(f"Error verifying payment: {e}")
        return PaymentVerificationResponse(
            success=False,
            message="Internal server error during payment verification",
            error=str(e)
        )


@payment_router.get("/payment_status/{order_id}")
async def get_payment_status(order_id: str):
    """
    Get payment status for an order (from payments table only).
    
    This endpoint returns payment information from the payments table,
    completely separate from order status.
    """
    try:
        # Verify order exists
        order = await get_order_by_id(order_id)
        if not order:
            raise HTTPException(
                status_code=404,
                detail=f"Order {order_id} not found"
            )
        
        # Get payment status from payments table only
        payment_summary = await get_payment_status_for_order(order_id)
        
        return {
            "success": True,
            "order_id": order_id,
            "order_status": order.get("status"),  # Order status (separate from payment)
            "payment_summary": payment_summary  # Payment status (from payments table)
        }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting payment status: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to get payment status"
        )
