FROM python:3.12-slim

WORKDIR /application

RUN apt-get update && apt-get install -y \
    libpq-dev \
    gcc \
    curl \
    && rm -rf /var/lib/apt/lists/*

COPY requirements.txt /application/requirements.txt

RUN pip install --no-cache-dir -r requirements.txt

COPY application /application

ENV PYTHONUNBUFFERED=1

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
