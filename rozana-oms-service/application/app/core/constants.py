"""
Core constants for the Rozana OMS application

This module contains all the core constants used across the application,
including order status codes, system-wide enums, and other shared values.
"""

class OrderStatus:
    """Order status constants for lifecycle management"""
    
    # Rozana (OMS) statuses
    DRAFT = 0
    OPEN = 10
    FULFILLED = 11
    PARTIALLY_FULFILLED = 12
    UNFULFILLED = 13
    CANCELED = 14
    RETURN = 15
    
    # WMS statuses
    WMS_SYNCED = 21
    WMS_SYNC_FAILED = 22

    # WMS processing statuses - numerical codes
    WMS_OPEN = 23
    WMS_INPROGRESS = 24
    WMS_PICKED = 25
    WMS_FULFILLED = 26
    WMS_INVOICED = 27

    
    # TMS statuses
    TMS_SYNCED = 31
    TMS_SYNC_FAILED = 32

    # String representation of statuses used in database
    DB_STATUS_MAP = {
        # OMS statuses
        "oms_draft": DRAFT,
        "oms_open": OPEN,
        "oms_fulfilled": FULFILLED,
        "oms_partial_fulfilled": PARTIALLY_FULFILLED,
        "oms_unfulfilled": UNFULFILLED,
        "oms_canceled": <PERSON><PERSON><PERSON><PERSON>,

         # WMS statuses - string to code mapping
        "wms_synced": W<PERSON>_SYNCED,
        "wms_sync_failed": WMS_SYNC_FAILED,
        "open" : WMS_OPEN,
        "in_progress": WMS_INPROGRESS,
        "picked": WMS_PICKED,
        "fulfilled": WMS_FULFILLED,
        "invoiced": WMS_INVOICED,

        # TMS statuses
        "tms_synced": TMS_SYNCED,
        "tms_sync_failed": TMS_SYNC_FAILED,
    }

    # Reverse mapping: code to string for database operations
    CODE_TO_DB_STATUS = {v: k for k, v in DB_STATUS_MAP.items()}
    
    @classmethod
    def get_status_name(cls, status_code: int) -> str:
        """Get human-readable status name from status code"""
        status_map = {
            cls.OPEN: "Open",
            cls.FULFILLED: "Fulfilled",
            cls.PARTIALLY_FULFILLED: "Partially Fulfilled",
            cls.UNFULFILLED: "UnFulfilled",
            cls.CANCELED: "Canceled",
            cls.WMS_SYNCED: "WMS Synced",
            cls.WMS_SYNC_FAILED: "WMS Sync Failed",
            cls.TMS_SYNCED: "TMS Synced",
            cls.TMS_SYNC_FAILED: "TMS Sync Failed",
            cls.DRAFT: "Draft"
        }
        return status_map.get(status_code, f"Unknown Status ({status_code})")

    @classmethod
    def get_customer_status_name(cls, status_code: int) -> str:
        """Get customer-friendly status name from status code (only Rozana statuses)"""
        # Only show customer-relevant statuses, hide internal WMS/TMS statuses
        customer_status_map = {
            cls.DRAFT: "Draft",
            cls.OPEN: "Confirmed",
            cls.FULFILLED: "Delivered",
            cls.PARTIALLY_FULFILLED: "Partially Delivered",
            cls.UNFULFILLED: "Processing",
            cls.CANCELED: "Cancelled",
            cls.RETURN: "Returned",
            cls.WMS_SYNCED: "Processing",
            cls.WMS_SYNC_FAILED: "Processing",
            cls.OPEN: "Confirmed",
            cls.WMS_INPROGRESS: "Processing",
            cls.WMS_PICKED: "Confirmed",
            cls.WMS_FULFILLED: "Confirmed",
            cls.WMS_INVOICED: "Confirmed",
            cls.TMS_SYNCED: "Assigned to Rider",
            cls.TMS_SYNC_FAILED: "Finding Rider"
        }
        return customer_status_map.get(status_code, "Processing")
    
    @classmethod
    def is_rozana_status(cls, status_code: int) -> bool:
        """Check if status code is a Rozana (OMS) status"""
        return status_code in [cls.DRAFT, cls.OPEN, cls.FULFILLED, cls.PARTIALLY_FULFILLED, cls.UNFULFILLED, cls.CANCELED, cls.RETURN]
    
    @classmethod
    def is_wms_status(cls, status_code: int) -> bool:
        """Check if status code is a WMS status"""
        return status_code in [
            cls.WMS_SYNCED, cls.WMS_SYNC_FAILED, cls.WMS_OPEN, cls.WMS_PICKED, cls.WMS_INPROGRESS, cls.WMS_FULFILLED, cls.WMS_INVOICED
        ]

    @classmethod
    def is_tms_status(cls, status_code: int) -> bool:
        """Check if status code is a TMS status"""
        return status_code in [cls.TMS_SYNCED, cls.TMS_SYNC_FAILED]
    



class PaymentStatus:
    """Payment status constants for payment lifecycle management"""
    
    # Payment statuses (integer-based for consistency with OrderStatus)
    PENDING = 50
    COMPLETED = 51
    FAILED = 52
    REFUNDED = 53
    
    # String representation of payment statuses used in database enum
    DB_STATUS_MAP = {
        50 : PENDING,
        51 : COMPLETED,
        52 : FAILED,
        53 : REFUNDED,
    }
    
    # Reverse mapping for database operations
    STATUS_TO_DB_MAP = {
        50: "pending",
        51: "completed",
        52: "failed",
        53: "refunded",
    }
    
    # Status descriptions
    STATUS_DESCRIPTIONS = {
        50: "Payment Pending",
        51: "Payment Completed", 
        52: "Payment Failed",
        53: "Payment Refunded"
    }
    
    @classmethod
    def get_description(cls, status_code: int) -> str:
        """Get human-readable description for payment status"""
        return cls.STATUS_DESCRIPTIONS.get(status_code, f"Unknown Payment Status ({status_code})")
    
    @classmethod
    def is_valid_status(cls, status_code: int) -> bool:
        """Check if status code is a valid payment status"""
        return status_code in [cls.PENDING, cls.COMPLETED, cls.FAILED, cls.REFUNDED]
    
    @classmethod
    def from_db_string(cls, db_status: str) -> int:
        """Convert database string status to integer constant"""
        return cls.DB_STATUS_MAP.get(db_status, cls.PENDING)
    
    @classmethod
    def to_db_string(cls, status_code: int) -> str:
        """Convert integer constant to database string status"""
        return cls.STATUS_TO_DB_MAP.get(status_code, "pending")
    
    @classmethod
    def is_final_status(cls, status_code: int) -> bool:
        """Check if payment status is final (completed, failed, or refunded)"""
        return status_code in [cls.COMPLETED, cls.FAILED, cls.REFUNDED]


class SystemConstants:
    """System-wide constants"""
    
    # Default ETA hours for new orders
    DEFAULT_ETA_HOURS = 24
    
    # Order ID prefix length
    ORDER_ID_PREFIX_LENGTH = 4
    
    # Maximum retry attempts for external service calls
    MAX_RETRY_ATTEMPTS = 3
    
    # Default timeout for external API calls (seconds)
    DEFAULT_API_TIMEOUT = 30


class APIConstants:
    """API-related constants"""
    
    # API version
    API_VERSION = "v1"
    
    # Default page size for paginated responses
    DEFAULT_PAGE_SIZE = 20
    
    # Maximum page size for paginated responses
    MAX_PAGE_SIZE = 100
