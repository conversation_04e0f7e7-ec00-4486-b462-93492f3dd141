import logging
from fastapi import HTTPException
from app.connections.database import get_raw_transaction
from app.integrations.wms_service import wms_service
from app.core.constants import OrderStatus
from sqlalchemy import text
from typing import List, Dict
from datetime import datetime, timedelta, timezone

logger = logging.getLogger(__name__)


def validate_item_return_eligibility(item_row, sku: str) -> Dict:
    """
    Validate if an item is eligible for return based on business rules.
    
    Args:
        item_row: Database row containing item details
        sku: SKU being validated
        
    Returns:
        Dict with validation result and details
    """
    validation_errors = []
    
    # Check is_returnable
    if not item_row.is_returnable:
        validation_errors.append(f"SKU {sku} is not returnable (is_returnable=False)")
    
    # Check return_type (10 = only return, 11 = return and exchange)
    if item_row.return_type not in ['10', '11']:
        validation_errors.append(f"SKU {sku} return type '{item_row.return_type}' does not allow returns (must be '10' or '11')")
    
    # Check item status (25=WMS_PICKED, 26=WMS_FULFILLED, 27=WMS_INVOICED, 31=TMS_SYNCED)
    allowed_item_statuses = [OrderStatus.WMS_PICKED, OrderStatus.WMS_FULFILLED, OrderStatus.WMS_INVOICED, 31]
    if item_row.status not in allowed_item_statuses:
        validation_errors.append(f"SKU {sku} status {item_row.status} does not allow returns (must be in {allowed_item_statuses})")
    
    # Check return window
    if item_row.return_window > 0:  # Only check if return window is specified
        item_updated_date = item_row.updated_at
        
        # Handle timezone-aware datetime from database
        if isinstance(item_updated_date, str):
            item_updated_date = datetime.fromisoformat(item_updated_date.replace('Z', '+00:00'))
        
        # Ensure we have timezone-aware datetime for comparison
        if item_updated_date.tzinfo is None:
            item_updated_date = item_updated_date.replace(tzinfo=timezone.utc)
        
        # Calculate return deadline (updated_at + return_window days)
        return_deadline = item_updated_date + timedelta(days=item_row.return_window)
        current_time = datetime.now(timezone.utc)
        
        # Allow return if current time is BEFORE or equal to deadline
        if current_time > return_deadline:
            validation_errors.append(f"SKU {sku} return window expired (deadline was {return_deadline.strftime('%Y-%m-%d %H:%M:%S UTC')}, current time: {current_time.strftime('%Y-%m-%d %H:%M:%S UTC')})")
    
    return {
        'is_eligible': len(validation_errors) == 0,
        'errors': validation_errors,
        'item_details': {
            'sku': sku,
            'is_returnable': item_row.is_returnable,
            'return_type': item_row.return_type,
            'return_window': item_row.return_window,
            'status': item_row.status,
            'updated_at': item_row.updated_at
        }
    }


async def return_order_items_core(order_id: str, items_to_return: List[Dict]):
    """
    Return specific items from an order.
    
    Args:
        order_id: The order ID to return items from
        items_to_return: List of items with sku and quantity to return
    """
    try:
        with get_raw_transaction() as conn:
            # Check if order exists and get current status
            check_order_sql = """
                SELECT id, order_id, status, facility_name 
                FROM orders 
                WHERE order_id = :order_id
            """
            
            result = conn.execute(text(check_order_sql), {'order_id': order_id})
            order_row = result.fetchone()
            
            if not order_row:
                raise HTTPException(
                    status_code=404, 
                    detail=f"Order {order_id} not found"
                )
            
            current_status = order_row.status
            facility_name = order_row.facility_name
            
            # Check if order status allows returns (25, 26, 27, 31)
            allowed_statuses = [OrderStatus.WMS_PICKED, OrderStatus.WMS_FULFILLED, OrderStatus.WMS_INVOICED, 31]
            
            if current_status not in allowed_statuses:
                raise HTTPException(
                    status_code=400,
                    detail=f"Order {order_id} cannot be returned. Current status: {current_status} is not in allowed statuses {allowed_statuses}"
                )
            
            # Get current order items with return eligibility fields
            get_items_sql = """
                SELECT id, sku, quantity, status, unit_price, sale_price, 
                       is_returnable, return_type, return_window, updated_at
                FROM order_items 
                WHERE order_id = :order_pk
            """
            
            items_result = conn.execute(text(get_items_sql), {'order_pk': order_row.id})
            current_items = {row.sku: row for row in items_result.fetchall()}
            
            # Validate items to return
            returned_items = []
            validation_errors = []
            
            for item in items_to_return:
                sku = item['sku']
                return_quantity = item['quantity']
                
                if sku not in current_items:
                    raise HTTPException(
                        status_code=400,
                        detail=f"SKU {sku} not found in order {order_id}"
                    )
                
                current_item = current_items[sku]
                if return_quantity > current_item.quantity:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Cannot return {return_quantity} units of {sku}. Order only contains {current_item.quantity} units"
                    )
                
                # Validate return eligibility
                eligibility_result = validate_item_return_eligibility(current_item, sku)
                if not eligibility_result['is_eligible']:
                    validation_errors.extend(eligibility_result['errors'])
            
            # If any items are not eligible for return, raise error
            if validation_errors:
                raise HTTPException(
                    status_code=400,
                    detail=f"Return not allowed: {'; '.join(validation_errors)}"
                )
            
            # Process the return for eligible items
            for item in items_to_return:
                sku = item['sku']
                return_quantity = item['quantity']
                current_item = current_items[sku]
                
                # Update item status to RETURN
                update_item_sql = """
                    UPDATE order_items 
                    SET status = :status, updated_at = NOW()
                    WHERE id = :item_id
                """
                
                conn.execute(text(update_item_sql), {
                    'status': OrderStatus.RETURN,
                    'item_id': current_item.id
                })
                
                returned_items.append({
                    'sku': sku,
                    'quantity': return_quantity,
                    'unit_price': float(current_item.unit_price),
                    'sale_price': float(current_item.sale_price),
                    'status': OrderStatus.RETURN
                })
            
            # Check if all items are returned (full return) or partial return
            remaining_items_sql = """
                SELECT COUNT(*) as count
                FROM order_items 
                WHERE order_id = :order_pk AND status != :return_status
            """
            
            remaining_result = conn.execute(text(remaining_items_sql), {
                'order_pk': order_row.id,
                'return_status': OrderStatus.RETURN
            })
            remaining_count = remaining_result.fetchone().count
            
            # Update order status based on return type
            if remaining_count == 0:
                # Full return - set order status to RETURN
                new_order_status = OrderStatus.RETURN
                return_type = "full"
            else:
                # Partial return - set order status to PARTIALLY_FULFILLED
                new_order_status = OrderStatus.PARTIALLY_FULFILLED
                return_type = "partial"
            
            update_order_sql = """
                UPDATE orders 
                SET status = :status, updated_at = NOW()
                WHERE order_id = :order_id
            """
            
            conn.execute(text(update_order_sql), {
                'status': new_order_status,
                'order_id': order_id
            })
            
            conn.commit()
            logger.info(f"Order {order_id} items returned successfully. Return type: {return_type}")
        
        # Integrate with WMS for return processing
        try:
            wms_result = await wms_service.process_return(
                order_reference=order_id,
                warehouse=facility_name,
                items=returned_items,
                return_type=return_type
            )
            
            if wms_result.get('success', False):
                logger.info(f"Return processed successfully in WMS for order {order_id}")
                wms_message = "Return processed in WMS successfully"
            else:
                logger.warning(f"WMS return processing failed for order {order_id}: {wms_result.get('message', 'Unknown error')}")
                wms_message = f"Return processed in OMS but WMS processing failed: {wms_result.get('message', 'Unknown error')}"
                
        except Exception as wms_error:
            logger.error(f"WMS return processing error for order {order_id}: {str(wms_error)}")
            wms_message = f"Return processed in OMS but WMS processing failed: {str(wms_error)}"
        
        return {
            "success": True,
            "message": f"Items returned successfully from order {order_id}",
            "order_id": order_id,
            "return_type": return_type,
            "returned_items": returned_items,
            "order_status": new_order_status,
            "wms_status": wms_message
        }
        
    except HTTPException:
        raise
    except Exception as exc:
        logger.error(f"Error returning items from order {order_id}: {exc}")
        raise HTTPException(
            status_code=500, 
            detail=f"Internal server error while processing return: {str(exc)}"
        ) from exc


async def return_full_order_core(order_id: str):
    """
    Return all items in an order.
    
    Args:
        order_id: The order ID to return completely
    """
    try:
        with get_raw_transaction() as conn:
            # Check if order exists and get current status
            check_order_sql = """
                SELECT id, order_id, status, facility_name 
                FROM orders 
                WHERE order_id = :order_id
            """
            
            result = conn.execute(text(check_order_sql), {'order_id': order_id})
            order_row = result.fetchone()
            
            if not order_row:
                raise HTTPException(
                    status_code=404, 
                    detail=f"Order {order_id} not found"
                )
            
            current_status = order_row.status
            facility_name = order_row.facility_name
            
            # Check if order status allows returns (25, 26, 27, 31)
            allowed_statuses = [OrderStatus.WMS_PICKED, OrderStatus.WMS_FULFILLED, OrderStatus.WMS_INVOICED, 31]
            
            if current_status not in allowed_statuses:
                raise HTTPException(
                    status_code=400,
                    detail=f"Order {order_id} cannot be returned. Current status: {current_status} is not in allowed statuses {allowed_statuses}"
                )
            
            if current_status == OrderStatus.RETURN:
                return {
                    "success": True,
                    "message": f"Order {order_id} is already returned",
                    "order_id": order_id,
                    "return_type": "full",
                    "returned_items": [],
                    "order_status": OrderStatus.RETURN
                }
            
            # Get all order items with return eligibility fields
            get_items_sql = """
                SELECT id, sku, quantity, status, unit_price, sale_price,
                       is_returnable, return_type, return_window, updated_at
                FROM order_items 
                WHERE order_id = :order_pk
            """
            
            items_result = conn.execute(text(get_items_sql), {'order_pk': order_row.id})
            all_items = items_result.fetchall()
            
            # Validate ALL items for return eligibility (full order return requirement)
            validation_errors = []
            for item in all_items:
                eligibility_result = validate_item_return_eligibility(item, item.sku)
                if not eligibility_result['is_eligible']:
                    validation_errors.extend(eligibility_result['errors'])
            
            # If ANY item is not eligible for return, reject the full order return
            if validation_errors:
                raise HTTPException(
                    status_code=400,
                    detail=f"Full order return not allowed: {'; '.join(validation_errors)}"
                )
            
            returned_items = []
            
            # Update all items status to RETURN (only if all items are eligible)
            for item in all_items:
                update_item_sql = """
                    UPDATE order_items 
                    SET status = :status, updated_at = NOW()
                    WHERE id = :item_id
                """
                
                conn.execute(text(update_item_sql), {
                    'status': OrderStatus.RETURN,
                    'item_id': item.id
                })
                
                returned_items.append({
                    'sku': item.sku,
                    'quantity': item.quantity,
                    'unit_price': float(item.unit_price),
                    'sale_price': float(item.sale_price),
                    'status': OrderStatus.RETURN
                })
            
            # Update order status to RETURN
            update_order_sql = """
                UPDATE orders 
                SET status = :status, updated_at = NOW()
                WHERE order_id = :order_id
            """
            
            conn.execute(text(update_order_sql), {
                'status': OrderStatus.RETURN,
                'order_id': order_id
            })
            
            conn.commit()
            logger.info(f"Full order {order_id} returned successfully")
        
        # Integrate with WMS for return processing
        try:
            wms_result = await wms_service.process_return(
                order_reference=order_id,
                warehouse=facility_name,
                items=returned_items,
                return_type="full"
            )
            
            if wms_result.get('success', False):
                logger.info(f"Full return processed successfully in WMS for order {order_id}")
                wms_message = "Full return processed in WMS successfully"
            else:
                logger.warning(f"WMS full return processing failed for order {order_id}: {wms_result.get('message', 'Unknown error')}")
                wms_message = f"Full return processed in OMS but WMS processing failed: {wms_result.get('message', 'Unknown error')}"
                
        except Exception as wms_error:
            logger.error(f"WMS full return processing error for order {order_id}: {str(wms_error)}")
            wms_message = f"Full return processed in OMS but WMS processing failed: {str(wms_error)}"
        
        return {
            "success": True,
            "message": f"Full order {order_id} returned successfully",
            "order_id": order_id,
            "return_type": "full",
            "returned_items": returned_items,
            "order_status": OrderStatus.RETURN,
            "wms_status": wms_message
        }
        
    except HTTPException:
        raise
    except Exception as exc:
        logger.error(f"Error returning full order {order_id}: {exc}")
        raise HTTPException(
            status_code=500, 
            detail=f"Internal server error while processing full return: {str(exc)}"
        ) from exc
