#!/usr/bin/env python3
"""
Test script for item-level status updates in OMS API
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000"

def print_response(title, response):
    """Print formatted response"""
    print(f"\n🔍 {title}")
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        print(f"Response: {json.dumps(response.json(), indent=2)}")
    else:
        print(f"Error: {response.text}")
    return response.status_code == 200

def test_item_status_updates():
    """Test the new item-level status update functionality"""
    print("🚀 Testing Item-Level Status Updates")
    print("=" * 50)
    
    # 1. Create an order with multiple items
    print("\n📦 Step 1: Creating order with multiple items...")
    order_data = {
        "customer_id": "CUST-001",
        "customer_name": "John Doe",
        "facility_id": "FAC-001", 
        "facility_name": "Main Warehouse",
        "status": "pending",
        "total_amount": 150.00,
        "items": [
            {
                "sku": "ITEM-001",
                "quantity": 2,
                "unit_price": 45.00,
                "sale_price": 50.00
            },
            {
                "sku": "ITEM-002", 
                "quantity": 1,
                "unit_price": 95.00,
                "sale_price": 100.00
            }
        ]
    }
    
    response = requests.post(f"{BASE_URL}/create_order", json=order_data)
    if not print_response("Create Order", response):
        return False
    
    order_id = response.json().get("order_id")
    print(f"✅ Order created: {order_id}")
    
    # 2. Get initial order details
    print(f"\n📋 Step 2: Getting initial order details...")
    response = requests.get(f"{BASE_URL}/get_order_details", params={"order_id": order_id})
    if not print_response("Initial Order Details", response):
        return False
    
    # 3. Update ITEM-001 status to "confirmed"
    print(f"\n✅ Step 3: Updating ITEM-001 status to 'confirmed'...")
    item_update_data = {
        "order_id": order_id,
        "sku": "ITEM-001",
        "status": "confirmed"
    }
    
    response = requests.put(f"{BASE_URL}/update_item_status", json=item_update_data)
    if not print_response("Update ITEM-001 Status", response):
        return False
    
    # 4. Update ITEM-002 status to "refunded"
    print(f"\n💰 Step 4: Updating ITEM-002 status to 'refunded'...")
    item_update_data = {
        "order_id": order_id,
        "sku": "ITEM-002",
        "status": "refunded"
    }
    
    response = requests.put(f"{BASE_URL}/update_item_status", json=item_update_data)
    if not print_response("Update ITEM-002 Status", response):
        return False
    
    # 5. Get final order details to verify changes
    print(f"\n📋 Step 5: Getting final order details to verify changes...")
    response = requests.get(f"{BASE_URL}/get_order_details", params={"order_id": order_id})
    if not print_response("Final Order Details", response):
        return False
    
    # 6. Test error case - try to update non-existent item
    print(f"\n❌ Step 6: Testing error case - updating non-existent item...")
    item_update_data = {
        "order_id": order_id,
        "sku": "ITEM-999",
        "status": "confirmed"
    }
    
    response = requests.put(f"{BASE_URL}/update_item_status", json=item_update_data)
    print(f"Status: {response.status_code}")
    print(f"Expected Error: {response.text}")
    
    # 7. Test error case - try to update item in non-existent order
    print(f"\n❌ Step 7: Testing error case - updating item in non-existent order...")
    item_update_data = {
        "order_id": "FAKE+9999",
        "sku": "ITEM-001",
        "status": "confirmed"
    }
    
    response = requests.put(f"{BASE_URL}/update_item_status", json=item_update_data)
    print(f"Status: {response.status_code}")
    print(f"Expected Error: {response.text}")
    
    print(f"\n🎉 Item-level status update testing completed!")
    print(f"\n📝 Summary:")
    print(f"   • Created order {order_id} with 2 items")
    print(f"   • Updated ITEM-001 to 'confirmed'")
    print(f"   • Updated ITEM-002 to 'refunded'")
    print(f"   • Verified error handling for invalid cases")
    
    return True

if __name__ == "__main__":
    try:
        # Test health first
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code != 200:
            print("❌ API is not healthy!")
            exit(1)
        
        print("✅ API is healthy, starting tests...")
        test_item_status_updates()
        
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to API. Make sure the service is running on http://localhost:8000")
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
