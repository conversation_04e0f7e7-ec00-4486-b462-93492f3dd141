"""create_payment_details_table

Revision ID: 0be2f20fe890
Revises: 5eaaf29abd7f
Create Date: 2025-07-29 02:10:18.097922

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0be2f20fe890'
down_revision: Union[str, Sequence[str], None] = '5eaaf29abd7f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('payment_details',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('order_id', sa.Integer(), nullable=False),
        sa.Column('payment_id', sa.String(length=50), nullable=False),
        sa.Column('payment_amount', sa.DECIMAL(precision=10, scale=2), nullable=False),
        sa.Column('payment_date', sa.TIMESTAMP(), nullable=False),
        sa.Column('payment_mode', sa.Enum('cash', 'online', 'cash_and_online', name='payment_mode_enum'), nullable=False),
        sa.Column('cash_amount', sa.DECIMAL(precision=10, scale=2), nullable=False),
        sa.Column('online_amount', sa.DECIMAL(precision=10, scale=2), nullable=False),
        sa.Column('total_amount', sa.DECIMAL(precision=10, scale=2), nullable=False),
        sa.Column('payment_status', sa.Integer(), nullable=False, comment='Payment status: 50=Pending, 51=Completed, 52=Failed, 53=Refunded'),
        sa.Column('created_at', sa.TIMESTAMP(timezone=True), nullable=False),
        sa.Column('updated_at', sa.TIMESTAMP(timezone=True), nullable=False),
        sa.ForeignKeyConstraint(['order_id'], ['orders.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_payment_details_id'), 'payment_details', ['id'], unique=False)
    op.create_index(op.f('ix_payment_details_order_id'), 'payment_details', ['order_id'], unique=False)
    op.create_index(op.f('ix_payment_details_payment_id'), 'payment_details', ['payment_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_payment_details_payment_id'), table_name='payment_details')
    op.drop_index(op.f('ix_payment_details_order_id'), table_name='payment_details')
    op.drop_index(op.f('ix_payment_details_id'), table_name='payment_details')
    op.drop_table('payment_details')
    # ### end Alembic commands ###
