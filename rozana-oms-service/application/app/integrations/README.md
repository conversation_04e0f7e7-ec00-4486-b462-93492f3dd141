# WMS Integration

This module provides integration with ShipsyWMS (3rd party SaaS WMS solution) using direct service calls.

## Architecture Overview

The WMS integration follows a simple direct call pattern:

```
OMS Order Creation → Background WMS Sync → Update Order Status
```

### Key Features

- **Background Processing**: WMS sync happens asynchronously to prevent blocking order creation
- **Direct Service Calls**: Simple integration without complex action-trigger systems
- **Automatic Status Updates**: Orders are automatically updated to reflect WMS sync status
- **Token Management**: Automatic authentication with 10-hour token expiry and refresh

### Components

1. **WMS Service** (`wms_service.py`)
   - Handles ShipsyWMS API communication
   - Manages authentication tokens with auto-refresh
   - Transforms OMS data to WMS format
   - Updates order status based on sync result

2. **WMS Config** (`wms_config.py`)
   - Configuration management for WMS settings
   - Environment variable handling
   - Validation support

## Configuration

Add these environment variables to your `.env` file:

```env
# WMS Integration (WMS)
WMS_CLIENT_ID=
WMS_CLIENT_SECRET=
WMS_BASE_URL=
WMS_WAREHOUSE=
```

## Order Status Lifecycle

The system uses integer status codes from `app.core.constants.OrderStatus`:

### R<PERSON>ana (OMS) Statuses:
- **10** - Open
- **11** - Fulfilled  
- **12** - Partially Fulfilled
- **13** - UnFulfilled

### WMS Statuses:
- **21** - Synced (Successfully synced to WMS)
- **22** - Sync Failed (Failed to sync to WMS)

### TMS Statuses:
- **31** - Synced (Successfully synced to TMS)
- **32** - Sync Failed (Failed to sync to TMS)

## Usage

### Background WMS Sync (Recommended)

Orders are automatically synced to WMS in the background during order creation:

```python
# In OrderService.create_order()
from app.integrations.wms_service import wms_service
from fastapi import BackgroundTasks

# Order created immediately, WMS sync happens in background
background_tasks.add_task(wms_service.sync_order_to_wms, wms_order_data, self)
```

### Direct WMS Operations

```python
from app.integrations.wms_service import wms_service
from app.core.constants import OrderStatus

# Sync order to WMS (automatically updates status to 21 or 22)
result = await wms_service.sync_order_to_wms(order_data, order_service)

# Create order directly in WMS
result = await wms_service.create_outbound_order(order_data)

# Cancel order in WMS
result = await wms_service.cancel_outbound_order(order_reference)
```

## Data Transformation

The system automatically transforms OMS order data to WMS format:

### OMS → WMS Mapping

| OMS Field | WMS Field | Notes |
|-----------|-----------|-------|
| `order_id` | `order_reference` | Direct mapping |
| `facility_name` | `warehouse` | Facility to warehouse mapping |
| `customer_name` | `customer.customer_name` | From address or order data |
| `address` | `customer.*` | Address fields mapping |
| `items` | `items` | SKU, quantity, pricing mapping |
| `total_amount` | `charges` | Split into delivery/handling charges |

### Sample WMS Payload

```json
{
    "warehouse": "HANOI",
    "order_type": "Express",
    "order_reference": "ORDER123",
    "promised_time": "2025-12-29 22:55:00",
    "customer": {
        "customer_name": "John Doe",
        "phone_number": "1234567890",
        "address_line_1": "123 Main St",
        "city": "Mumbai",
        "state": "Maharashtra",
        "pincode": "400001"
    },
    "items": [
        {
            "sku": "SKU001",
            "quantity": 2,
            "mrp": 100,
            "sale_price": 90
        }
    ],
    "charges": [
        {
            "name": "DELIVERY_CHARGE",
            "amount": "40",
            "tax_amount": "0"
        }
    ]
}
```

## Error Handling

The system includes comprehensive error handling:

1. **Token Management**: Automatic token refresh with 5-minute buffer
2. **Background Processing**: WMS failures don't block order creation
3. **Status Tracking**: Failed syncs are marked with status 22
4. **Logging**: Detailed logs for debugging and monitoring

## Benefits of Background Processing

- **Fast Response**: Order creation returns immediately
- **Resilience**: Order acceptance not affected by WMS service failures/delays
- **Better UX**: Users don't wait for external service calls
- **Scalability**: System can handle high order volumes

## Security Considerations

- Client credentials stored as environment variables
- Tokens managed in memory with automatic refresh
- All API calls use HTTPS
- No sensitive data logged
