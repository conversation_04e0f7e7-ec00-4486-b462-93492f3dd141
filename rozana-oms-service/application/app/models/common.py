from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, DECIMAL, TIMESTAMP, Text, Boolean, ForeignKey, Computed
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func, text
from app.connections.database import Base
from datetime import datetime
from typing import Optional
import pytz

# IST timezone
IST = pytz.timezone('Asia/Kolkata')

def get_ist_now():
    """Get current datetime in IST timezone"""
    return datetime.now(IST)

class CommonModel(Base):
    """Base model with common fields for all models"""
    __abstract__ = True
    
    # Common timestamp fields for all models - using IST timezone
    created_at = Column(
        TIMESTAMP(timezone=True), 
        default=func.timezone('Asia/Kolkata', func.current_timestamp()), 
        nullable=False
    )
    updated_at = Column(
        TIMESTAMP(timezone=True), 
        default=func.timezone('Asia/Kolkata', func.current_timestamp()), 
        onupdate=func.timezone('Asia/Kolkata', func.current_timestamp()), 
        nullable=False
    )
    
    def to_dict(self) -> dict:
        """Convert model to dictionary for API responses with IST timestamps"""
        result = {}
        for column in self.__table__.columns:
            value = getattr(self, column.name)
            if isinstance(value, datetime):
                # Ensure datetime is in IST for API responses
                if value.tzinfo is None:
                    # If naive datetime, assume it's IST
                    value = IST.localize(value)
                else:
                    # Convert to IST if it's timezone-aware
                    value = value.astimezone(IST)
                result[column.name] = value.isoformat()
            elif hasattr(value, '__float__'):
                result[column.name] = float(value)
            else:
                result[column.name] = value
        return result