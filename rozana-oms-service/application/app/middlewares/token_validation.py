"""
Token validation middleware and decorators for API authentication
"""
import httpx
import logging
import os
from typing import Dict, Any, Callable
from functools import wraps
from fastapi import HTTPEx<PERSON>, Request

logger = logging.getLogger(__name__)


class TokenValidationService:
    """Service for validating tokens against external API"""
    
    def __init__(self, validation_url: str = None):
        if validation_url is None:
            validation_url = os.getenv("TOKEN_VALIDATION_URL", "http://127.0.0.1:8000")
        
        if validation_url and not validation_url.endswith("/api/check-token/"):
            validation_url = validation_url.rstrip("/")
            if not validation_url.endswith("/api/check-token"):
                validation_url += "/api/check-token/"
            else:
                validation_url += "/"
        
        self.validation_url = validation_url
    
    async def validate_token(self, token: str) -> bool:
        """Validate token against external API"""
        logger.info(f"Validating token using URL: {self.validation_url}")
        logger.info(f"Token (first 10 chars): {token[:10]}...")
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    self.validation_url,
                    params={"token": token},
                    timeout=10.0
                )
                
                logger.info(f"Token validation response status: {response.status_code}")
                logger.info(f"Token validation response body: {response.text}")
                
                if response.status_code == 200:
                    data = response.json()
                    is_valid = data.get("valid", False)
                    logger.info(f"Token validation result: {is_valid}")
                    return is_valid
                else:
                    logger.warning(f"Token validation failed with status {response.status_code}: {response.text}")
                    return False
                    
        except httpx.RequestError as e:
            logger.error(f"Token validation request failed: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error during token validation: {e}")
            return False


def require_token_validation(func: Callable) -> Callable:
    """
    Decorator for endpoints that require 3PL token validation
    Extracts token from query parameters or request body
    """
    @wraps(func)
    async def wrapper(*args, **kwargs):
        # Extract token from function arguments
        token = None
        
        # Check if token is in kwargs (query parameters)
        if 'token' in kwargs:
            token = kwargs['token']
        else:
            # Check if there's a request object with token in body
            for arg in args:
                if hasattr(arg, 'token'):
                    token = arg.token
                    break
        
        if not token:
            raise HTTPException(status_code=401, detail="Token is required")
        
        # Validate token
        token_service = TokenValidationService()
        is_valid = await token_service.validate_token(token)
        
        if not is_valid:
            raise HTTPException(status_code=401, detail="Invalid token")
        
        # Call the original function
        return await func(*args, **kwargs)
    
    return wrapper
